import type { Metadata } from 'next';
import HomeCN, { cnContent } from '@/components/home/<USER>';
import HomeEN, { enContent } from '@/components/home/<USER>';

// 根据语言生成 SEO 元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string }>;
}): Promise<Metadata> {
  const { lang } = await params;

  // 根据语言选择对应的内容配置
  const content = lang === 'en' ? enContent : cnContent;

  return content.metadata;
}

export default async function HomePage({
  params,
}: {
  params: Promise<{ lang: string }>;
}) {
  const { lang } = await params;

  // 根据语言参数动态渲染对应的语言组件
  if (lang === 'en') {
    return <HomeEN />;
  }

  // 默认返回中文版
  return <HomeCN />;
}


