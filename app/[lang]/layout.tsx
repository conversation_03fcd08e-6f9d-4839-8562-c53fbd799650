import '@/app/global.css';
import { RootProvider } from 'fumadocs-ui/provider';
import { Inter } from 'next/font/google';
import type { ReactNode } from 'react';
import type { Translations } from 'fumadocs-ui/i18n';
import AmplitudeProvider from '@/components/AmplitudeProvider';

const inter = Inter({
  subsets: ['latin'],
});

export const metadata = {
  icons: {
    icon: '/images/icon.png',
  },
};

// translations
const cn: Partial<Translations> = {
  search: 'Translated Content',
};
// available languages that will be displayed on UI
// make sure `locale` is consistent with your i18n config
const locales = [
  {
    name: 'English',
    locale: 'en',
  },
  {
    name: 'Chinese',
    locale: 'cn',
  },
];

export default async function Layout({
  params,
  children,
}: {
  params: Promise<{ lang: string }>;
  children: React.ReactNode;
}) {
  const lang = (await params).lang;
  return (
    <html lang="zh-CN" className={inter.className} suppressHydrationWarning>
      <body className="flex flex-col min-h-screen">
        <RootProvider
          i18n={{
            locale: lang,
            locales,
            translations: { cn }[lang],
          }}
        >
          <AmplitudeProvider />
          {children}
        </RootProvider>
      </body>
    </html>
  );
}
