@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 移动端菜单动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 菜单按钮旋转效果 */
#mobile-menu-toggle:checked ~ nav label svg {
  transform: rotate(90deg);
}

nav label svg {
  transition: transform 0.3s ease;
}

/* 移动端菜单显示时的动画 */
#mobile-menu-toggle:checked ~ nav .peer-checked\:block {
  animation: slideDown 0.3s ease-out;
}

/* AI 服务商 Logo 容器样式 */
.ai-provider-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

/* AI 服务商 Logo 图片优化 */
.ai-provider-logo-container img {
  transition: opacity 0.2s ease-in-out;
}

.ai-provider-logo-container img:hover {
  opacity: 0.8;
}
