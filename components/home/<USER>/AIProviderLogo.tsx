import Image from 'next/image';

// AI 服务商名称到文件名的映射
const AI_PROVIDER_LOGO_MAP: Record<string, string> = {
  'OpenAI': 'openai',
  '<PERSON>': 'claude',
  'Gemini': 'gemini',
  'DeepSeek': 'deepseek',
  'Qwen': 'qwen',
  'G<PERSON>': 'zhipu',        // GLM 对应 zhipu.svg
  'Moonshot': 'moonshot',
  'Doubao': 'doubao',    // 火山方舟（豆包）
  'Hunyuan': 'hunyuan',  // 腾讯混元
  'Baidu': 'qianfan',    // Baidu 对应 qianfan.svg（百度千帆）
  'SiliconFlow': 'siliconflow',
  'Minimax': 'minimax',
  'Grok': 'grok',
  'Ollama': 'ollama',
  'OpenRouter': 'openrouter',
  'Volcengine': 'volcengine'  // 火山引擎
};

// AI 服务商 Logo 组件 - SSR 友好版本，支持可靠的降级机制
export function AIProviderLogo({ name, className = "" }: { name: string; className?: string }) {
  // 获取对应的文件名，如果没有映射或为空字符串，则使用默认占位
  const logoFileName = AI_PROVIDER_LOGO_MAP[name];
  const hasLogo = logoFileName && logoFileName.trim() !== '';

  // 如果有对应的 Logo 文件，使用 SVG 格式；否则使用默认 AI Logo
  const logoPath = hasLogo ? `/images/logo/${logoFileName}.svg` : '/images/logo/default-ai.svg';

  return (
    <div className={`flex flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm hover:shadow-md transition-shadow min-h-[100px] ${className}`}>
      {/* Logo 图标容器 */}
      <div className="flex items-center justify-center mb-2">
        <Image
          src={logoPath}
          alt={`${name} Logo`}
          width={40}
          height={40}
          className="object-contain max-w-full max-h-full"
          // 添加优先级和加载优化
          priority={false}
          loading="lazy"
        />
      </div>

      {/* 服务商名称 - 始终显示 */}
      <div className="text-center">
        <span className="text-xs font-medium text-gray-700 leading-tight">
          {name}
        </span>
      </div>
    </div>
  );
}
