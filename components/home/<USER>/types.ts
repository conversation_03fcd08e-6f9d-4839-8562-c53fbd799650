import type { Metadata } from 'next';

// 导航项类型
export interface NavigationItem {
  name: string;
  href: string;
  external?: boolean;
}

// 功能特性类型
export interface Feature {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  desc: string;
}

// 部署选项类型
export interface DeploymentOption {
  title: string;
  description: string;
  features: string[];
  icon: React.ComponentType<{ className?: string }>;
}

// 演示链接类型
export interface DemoLink {
  title: string;
  url: string;
  description: string;
}

// 首页组件 Props 类型
export interface HomePageProps {
  lang: string;
}

// 语言特定的内容配置类型
export interface HomePageContent {
  metadata: Metadata;
  navigation: NavigationItem[];
  hero: {
    subtitle: string;
    ctaButtons: {
      demo: string;
      docs: string;
    };
    tags: string[];
  };
  sections: {
    aiModels: {
      title: string;
      subtitle: string;
    };
    coreFeatures: {
      title: string;
      features: Feature[];
    };
    featuresGrid: {
      title: string;
      features: Feature[];
    };
    deployment: {
      title: string;
      subtitle: string;
      options: DeploymentOption[];
    };
    demo: {
      title: string;
      subtitle: string;
      onlineDemo: {
        title: string;
        description: string;
        userDemo: {
          title: string;
          url: string;
          note: string;
        };
        adminDemo: {
          title: string;
          url: string;
          credentials: string;
        };
      };
      github: {
        title: string;
        description: string;
        button: string;
        badges: {
          openSource: string;
          license: string;
        };
      };
    };
    cta: {
      title: string;
      subtitle: string;
      buttons: {
        demo: string;
        docs: string;
      };
    };
    footer: {
      copyright: string;
      links: {
        privacy: string;
        terms: string;
      };
    };
  };
  structuredData: any;
}
