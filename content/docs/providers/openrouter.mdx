---
title: OpenRouter
---

1.  注册并登录 [OpenRouter](https://openrouter.ai/)
2. 在后台管理页，切换到 API Keys 页面，击 「Create API Key」创建新的 API Key，复制生成的 API Key
![image](/images/providers/openrouter/01.png)
3. 将 API Key 填写到配置框中，中转地址留空即可，目前中国大陆可以直接访问 OpenRouter 的 API。
![image](/images/providers/openrouter/02.png)
4. 添加模型：点击 Model 导航，检索需要使用的模型，点击进入详情页
![image](/images/providers/openrouter/03.png)
5. 复制模型 ID 备用
![image](/images/providers/openrouter/04.png)
6. 点击自定义添加模型
<center>
<img src="/images/providers/openrouter/05.png" alt="image" width="600"/>
</center>
7. 将模型相关信息填入到配置框中
<center>
<img src="/images/providers/openrouter/06.png" alt="image" width="400"/>
</center>