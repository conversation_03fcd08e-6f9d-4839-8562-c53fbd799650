---
title: Vercel 部署
---

点击下面的按钮，即可开始部署。

[![Deploy with Vercel](/VercelButton.svg)](https://vercel.com/new/clone?repository-url=https://github.com/HiveNexus/hivechat.git&project-name=hivechat&env=DATABASE_URL&env=AUTH_SECRET&env=ADMIN_CODE&env=EMAIL_AUTH_STATUS&env=FEISHU_AUTH_STATUS&env=FEISHU_CLIENT_ID&env=FEISHU_CLIENT_SECRET)

默认将代码克隆的自己的 Github 后，需要填写环境变量：

<img width="726" alt="image" src="https://jiantuku.oss-cn-beijing.aliyuncs.com/share/vercel01.png" />

```
# PostgreSQL 数据库连接 URL，Vercel 平台提供了免费的托管服务，详情见下面说明
DATABASE_URL=postgres://postgres:password@localhost/hivechat

#用于用户信息等敏感信息的加密，可以使用 openssl rand -base64 32 生成一个随机的 32 位字符串作为密钥，此处为示例，请替换为自己生成的值。
AUTH_SECRET=hclqD3nBpMphLevxGWsUnGU6BaEa2TjrCQ77weOVpPg=

# 管理员授权码，初始化后，凭此值设置管理员账号，此处为示例，请替换为自己生成的值。
ADMIN_CODE=22113344

# 生产环境设置为正式域名，开启飞书等第三方登录时回调时会使用
# 首次可使用 `https://Vercel中的项目名.vercel.app`
NEXTAUTH_URL=https://hivechat-xxx.vercel.app

是否开启邮箱登录，开启值设为 ON，关闭时设为 OFF
EMAIL_AUTH_STATUS=ON

# 是否开启飞书登录，开启值设为 ON，关闭时修改为 OFF
FEISHU_AUTH_STATUS=OFF
FEISHU_CLIENT_ID="cli_xxxxxxxxxxxxxxxx"
FEISHU_CLIENT_SECRET="xxxxxxxxHOEWIoE7eDc1Lhc0042OXXXX"

# 是否开启企业微信登录，开启值设为 ON，关闭时修改为 OFF
WECOM_AUTH_STATUS=OFF
WECOM_CLIENT_ID="ww728c371c2fXXXXXX"
WECOM_AGENT_ID="100XXXX"
WECOM_CLIENT_SECRET="H-7J4jzG0m1axpXLGshaCDlMOZxdjvkX6bIVLuXXXXXX"

# 是否开启钉钉登录，开启值设为 ON，关闭时修改为 OFF
DINGDING_AUTH_STATUS=OFF
DINGDING_CLIENT_ID="dingpcfi2kpuplXXXXXX"
DINGDING_CLIENT_SECRET="3vk9-VFCExNckqNUk_CL2F-HEgz7qGN-BimH0lZ1gUx6hWO7g_an2lnkk6XXXXXX"
```
#### 附：Vercel（Neon）PostgreSQL 配置

1. 在 Vercel 平台顶部导航，选择「Storage」标签，点击 Create Databse
2. 选择 Neon(Serverless Postgres)
<img width="400" alt="image" src="https://jiantuku.oss-cn-beijing.aliyuncs.com/share/vercel02.png" />

3. 按照指引完成创建后，复制此处 `DATABASE_URL` 的值，填入到上一步的 `DATABASE_URL` 中
<img width="800" alt="image" src="https://jiantuku.oss-cn-beijing.aliyuncs.com/share/vercel03.png" />

4. 初始化管理员账号

按照以上方法安装部署完成后，访问 `http://localhost:3000/setup` (实际使用的域名和端口号)，即可进入管理员账号设置页面，设置完成后，即可正常使用系统。
