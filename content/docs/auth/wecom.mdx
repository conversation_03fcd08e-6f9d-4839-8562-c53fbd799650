---
title: 使用企业微信登录
---

## 操作步骤

### 1. Step 1

使用管理员账号登入企业微信管理后台，进入「我的企业」页面，到页面底部复制「企业 ID」，填入到 HiveChat 根目录 .env 配置文件的 `WECOM_CLIENT_ID` 变量中，同时把.env 配置文件的 `WECOM_AUTH_STATUS` 修改为 `ON` 
![image](/images/wecom/01.png)

### 2. Step 2

使用企业微信管理员账号登入管理后台，进入「应用管理」页，点击创建应用
![image](/images/wecom/02.png)

### 3. Step 3

填写基础应用信息，应用名称填写 HiveChat，或者你希望的名字
<center>
<img src="/images/wecom/03.png" alt="image" width="500"/>
</center>

图标可以参考使用下图

<center>
<img src="/images/wecom/04.png" alt="image" width="140"/>
</center>

### 4. Step 4

进入到应用详情页

1. 复制此处的 AgentId  到 HiveChat 根目录 .env 配置文件的 `WECOM_AGENT_ID` 变量中
2. 点击「查看」 Secret，复制 Secret 的值，填入到根目录 .env 配置文件的 `WECOM_CLIENT_SECRET` 变量中

![image](/images/wecom/05.png)

### 5. Step 5

在应用详情页面，点击「企业微信授权登录」设置按钮
![image](/images/wecom/06.png)
填入要绑定的 HiveChat 的域名，注意，不需要添加 http 开头，也不需要添加路径
![image](/images/wecom/07.png)

### 6. Step 6

点击设置可信域名
![image](/images/wecom/08.png)
在「可信域名」选项中，设置 hivechat 的域名，注意，此步骤需要校验域名的所有权
<center>
<img src="/images/wecom/09.png" alt="image" width="600"/>
</center>

### 7. Step 7

配置「企业可信IP」，将预计部署的服务器外网 IP 填写到该设置项中。
![image](/images/wecom/10.png)
最终，根目录下 .env 文件有 4 项必要的关于企业微信登录的设置都填入了有效值，示例如下：
![image](/images/wecom/11.png)

## 效果预览
只开启了企业微信登录时
<center>
<img src="/images/wecom/12.png" alt="image" width="400"/>
</center>
如果开启了多种登录方式，不同登录方式的登录入口都会显示
<center>
<img src="/images/wecom/13.png" alt="image" width="400"/>
</center>