---
title: 使用飞书登录
---

## 操作步骤
### 1. Step 1
使用飞书管理员登录[飞书开放平台](https://open.feishu.cn/app)，点击「创建企业自建应用」
![image](/images/feishu/01.png)

### 2. Step 2
填写应用基本信息，完成创建
![image](/images/feishu/02.png)

### 3. Step 3
获取应用 App ID 和 App Secret，分别写到到项目 .env 配置文件中的以下几个参数值：
-  `FEISHU_CLIENT_ID` ：填写应用的 App ID
-  `FEISHU_CLIENT_SECRET` ：填写应用的 App Secret
-  `FEISHU_AUTH_STATUS` 修改为 `ON`
-  `NEXTAUTH_URL` 写实际用的域名，和飞书后台填写的 callback 地址域名一致，末尾不需要加 /，如 `https://hivechat-demo.vercel.app`
如果是 Vercel 部署，则配置对应的环境变量。
![image](/images/feishu/03.png)

### 4. Step 4
从左侧导航进入「安全设置」页面，添加重定向 URL ，将测试地址或生产环境地址填入到此处，可以填写多个，请注意，此处填写以下内容
`http(s)://域名/api/auth/callback/feishu`
![image](/images/feishu/04.png)

### 5. Step 5
从左侧导航进入「权限管理」，添加如下 2 个权限
  1. 获取用户邮箱信息（contact:user.email:readonly）
  2. 获取用户 user ID（contact:user.employee_id:readonly）

![image](/images/feishu/05.png)

### 6. Step 6
 完成后，发布应用，继续完成项目部署，即可使用飞书登录。

## 效果预览
只开启飞书登录时：
<center>
<img src="/images/feishu/06.png" alt="image" width="400"/>
</center>
如果开启了多种登录方式，不同登录方式的登录入口都会显示
<center>
<img src="/images/feishu/07.png" alt="image" width="400"/>
</center>