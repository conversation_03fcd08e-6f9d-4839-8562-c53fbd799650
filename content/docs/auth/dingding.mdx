---
title: 使用钉钉登录
---

## 操作步骤
### 1. Step 1
使用管理员账号，登录钉钉开发者平台，点击创建应用
![image](/images/dingding/01.png)
填入应用基本信息，点击保存，应用图标可参考使用以下图片
<center>
<img src="/images/dingding/02.png" alt="image" width="140"/>
</center>

<center>
<img src="/images/dingding/03.png" alt="image" width="500"/>
</center>

### 2. Step 2
进入「凭证与基础信息」页
- 复制 Client ID ，填入到 到 HiveChat 根目录 .env 配置文件的 `DINGDING_CLIENT_ID` 变量中
- 复制 Client Secret，填入到 .env 配置文件的 `DINGDING_CLIENT_SECRET` 变量中
- 将 .env 配置文件 `DINGDING_AUTH_STATUS` 的值修改为 `ON`
![image](/images/dingding/04.png)

### 3. Step 3
进入到「权限管理」，给需要使用 Hivechat 的员工或部门添加「通讯录个人信息读权限」权限
![image](/images/dingding/05.png)

### 4. Step 4
进入到「安全设置」页
![image](/images/dingding/06.png)
在「重定向 URL（回调域名）」选项中，设置为如下的域名
`http(s)://域名/api/auth/callback/dingding`

### 5. Step 5
完成以上配置后，发布应用
## 效果预览
只开启钉钉登录时：
<center>
<img src="/images/dingding/07.png" alt="image" width="400"/>
</center>
如果开启了多种登录方式，不同登录方式的登录入口都会显示
<center>
<img src="/images/dingding/08.png" alt="image" width="400"/>
</center>
