import * as amplitude from '@amplitude/analytics-browser';

export const initAmplitude = () => {
  const amplitudeApiKey = process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY!;
  amplitude.init(amplitudeApiKey, undefined, {
    defaultTracking: true,
    cookieOptions:{
      domain:'.hivechat.net'
    }
  });
};

export const trackEvent = (eventName: string, eventProperties?: object) => {
  amplitude.track(eventName, eventProperties);
};

/**
 * 设置用户 ID 和基本属性
 * @param userId 用户 ID（通常是 email）
 * @param userProperties 用户属性
 */
export const setUser = (userId: string) => {
  amplitude.setUserId(userId);
};

/**
 * 追踪用户登录事件
 * @param provider 登录方式（github, google, credentials）
 * @param workspaceId 工作空间 ID（如果有）
 */
export const trackLogin = (provider: string,) => {
  trackEvent('user_login', {
    provider,
  });
};

